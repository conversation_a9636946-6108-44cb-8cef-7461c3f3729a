// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:email_validator/email_validator.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/cupertino.dart';
import 'package:super_up/app/modules/auth/waiting_list/views/waiting_list_page.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

import 'package:v_platform/v_platform.dart';

import '../../../../core/api_service/auth/auth_api_service.dart';
import '../../../../core/api_service/profile/profile_api_service.dart';
import '../../../../core/app_config/app_config_controller.dart';
import '../../../home/<USER>/views/home_view.dart';
import '../../auth_utils.dart';
import 'package:super_up/app/modules/auth/profile_picture_upload/views/profile_picture_upload_view.dart';
import '../views/register_otp_modal.dart';

class RegisterController implements SBaseController {
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmController = TextEditingController();
  final AuthApiService authService;
  final ProfileApiService profileService;
  bool isEmailVerified = false;

  RegisterController(
    this.authService,
    this.profileService,
  );

  Future<void> register(BuildContext context) async {
    final name = nameController.text.trim();
    final email = emailController.text.trim();
    if (name.isEmpty) {
      VAppAlert.showErrorSnackBar(
        message: S.of(context).nameMustHaveValue,
        context: context,
      );
      return;
    }
    if (!EmailValidator.validate(email)) {
      VAppAlert.showErrorSnackBar(
        message: S.of(context).emailNotValid,
        context: context,
      );
      return;
    }
    final password = passwordController.text;
    final confirm = confirmController.text;

    if (password.isEmpty) {
      VAppAlert.showErrorSnackBar(
        message: S.of(context).passwordMustHaveValue,
        context: context,
      );
      return;
    }

    if (password != confirm) {
      VAppAlert.showErrorSnackBar(
        message: S.of(context).passwordNotMatch,
        context: context,
      );
      return;
    }
    if (_checkIfLoginNoAllowed()) {
      VAppAlert.showErrorSnackBar(
        message: S.of(context).loginNowAllowedNowPleaseTryAgainLater,
        context: context,
      );
      return;
    }

    // If email is not verified, send OTP first
    if (!isEmailVerified) {
      await _sendOtpAndShowModal(context);
      return;
    }

    await vSafeApiCall<SMyProfile>(
      onLoading: () async {
        VAppAlert.showLoading(context: context);
      },
      onError: (exception, trace) {
        final errEnum = EnumToString.fromString(
            ApiI18nErrorRes.values, exception.toString());
        Navigator.of(context).pop();
        VAppAlert.showOkAlertDialog(
          context: context,
          title: S.of(context).error,
          content: AuthTrUtils.tr(errEnum) ?? exception.toString(),
        );
      },
      request: () async {
        final deviceHelper = DeviceInfoHelper();
        await authService.register(RegisterDto(
          email: email,
          method: RegisterMethod.email,
          fullName: name,
          pushKey: await (await VChatController
                  .I.vChatConfig.currentPushProviderService)
              ?.getToken(
            VPlatforms.isWeb ? SConstants.webVapidKey : null,
          ),
          deviceInfo: await deviceHelper.getDeviceMapInfo(),
          deviceId: await deviceHelper.getId(),
          language: VLanguageListener.I.appLocal.languageCode,
          platform: VPlatforms.currentPlatform,
          password: password,
        ));
        return profileService.getMyProfile();
      },
      onSuccess: (response) async {
        final status = response.registerStatus;

        // Get the access token that was stored during registration
        final accessToken =
            VAppPref.getHashedString(key: SStorageKeys.vAccessToken.name);

        // Add account to multi-account manager
        await MultiAccountManager.instance.addAccount(
          email: email,
          accessToken: accessToken ?? '',
          profile: response,
        );

        // Switch to this account
        final accountId =
            AccountSession.createAccountId(email, response.baseUser.id);
        await MultiAccountManager.instance.switchToAccount(accountId);

        if (status == RegisterStatus.accepted) {
          _profilePictureNav(context);
        } else {
          context.toPage(
            WaitingListPage(
              profile: response,
            ),
            withAnimation: true,
            removeAll: true,
          );
        }
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  @override
  void onClose() {
    emailController.dispose();
    nameController.dispose();
    confirmController.dispose();
    passwordController.dispose();
  }

  @override
  void onInit() {}

  void _profilePictureNav(BuildContext context) {
    context.toPage(
      const ProfilePictureUploadView(),
      withAnimation: true,
      removeAll: true,
    );
  }

  void _homeNav(BuildContext context) {
    context.toPage(
      const HomeView(),
      withAnimation: true,
      removeAll: true,
    );
  }

  bool _checkIfLoginNoAllowed() {
    if (VPlatforms.isMobile &&
        !VAppConfigController.appConfig.allowMobileLogin) {
      return true;
    }
    if (VPlatforms.isWeb && !VAppConfigController.appConfig.allowWebLogin) {
      return true;
    }
    if (VPlatforms.isDeskTop &&
        !VAppConfigController.appConfig.allowDesktopLogin) {
      return true;
    }
    return false;
  }

  Future<void> _sendOtpAndShowModal(BuildContext context) async {
    await vSafeApiCall(
      onLoading: () {
        VAppAlert.showLoading(context: context);
      },
      request: () async {
        await authService.sendRegisterOtp(emailController.text.trim());
      },
      onSuccess: (response) {
        context.pop(); // Close loading
        _showOtpModal(context);
      },
      onError: (exception, trace) {
        context.pop(); // Close loading
        final errEnum = EnumToString.fromString(
          ApiI18nErrorRes.values,
          exception.toString(),
        );
        VAppAlert.showErrorSnackBar(
          message: AuthTrUtils.tr(errEnum) ?? exception.toString(),
          context: context,
        );
      },
    );
  }

  void _showOtpModal(BuildContext context) {
    showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => RegisterOtpModal(
        email: emailController.text.trim(),
        onOtpVerified: (otp, resetLoading) =>
            _verifyOtp(context, otp, resetLoading),
        onResendOtp: () => _resendOtp(context),
      ),
    );
  }

  Future<void> _verifyOtp(
      BuildContext context, String otp, VoidCallback resetLoading) async {
    print("🔍 Starting OTP verification for: ${emailController.text.trim()}");

    await vSafeApiCall(
      onLoading: () {
        print("🔄 OTP verification loading started");
        // Loading is handled by the modal
      },
      request: () async {
        print("📡 Making API call to verify OTP");
        await authService.verifyRegisterOtp(
          emailController.text.trim(),
          otp,
        );
      },
      onSuccess: (response) async {
        print("✅ OTP verification successful");
        resetLoading(); // Reset loading state first
        isEmailVerified = true;
        Navigator.of(context).pop(); // Close OTP modal
        VAppAlert.showSuccessSnackBar(
          message: "Email verified successfully",
          context: context,
        );
        // Small delay to ensure modal is closed
        await Future.delayed(const Duration(milliseconds: 500));
        print("🚀 Proceeding with registration");
        // Now proceed with registration
        register(context);
      },
      onError: (exception, trace) {
        print("❌ OTP verification failed: $exception");
        resetLoading(); // Reset loading state on error
        final errEnum = EnumToString.fromString(
          ApiI18nErrorRes.values,
          exception.toString(),
        );
        VAppAlert.showErrorSnackBar(
          message: AuthTrUtils.tr(errEnum) ?? exception.toString(),
          context: context,
        );
      },
    );
  }

  Future<void> _resendOtp(BuildContext context) async {
    await vSafeApiCall(
      onLoading: () {
        // Show loading indicator
      },
      request: () async {
        await authService.sendRegisterOtp(emailController.text.trim());
      },
      onSuccess: (response) {
        VAppAlert.showSuccessSnackBar(
          message: "OTP sent successfully",
          context: context,
        );
      },
      onError: (exception, trace) {
        final errEnum = EnumToString.fromString(
          ApiI18nErrorRes.values,
          exception.toString(),
        );
        VAppAlert.showErrorSnackBar(
          message: AuthTrUtils.tr(errEnum) ?? exception.toString(),
          context: context,
        );
      },
    );
  }
}
